import scrapy
from urllib.parse import urljoin


class NkpSpider(scrapy.Spider):
    name = 'nkp'
    allowed_domains = ['nkp.gov.np']
    start_urls = ['https://nkp.gov.np/']
    
    custom_settings = {
        'DOWNLOAD_DELAY': 2,
        'RANDOMIZE_DOWNLOAD_DELAY': True,
        'CONCURRENT_REQUESTS_PER_DOMAIN': 1,
        'USER_AGENT': 'Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0'
    }
    
    def start_requests(self):
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Priority': 'u=0, i'
        }
        
        for url in self.start_urls:
            yield scrapy.Request(
                url=url,
                headers=headers,
                callback=self.parse
            )
    
    def parse(self, response):
        # Extract main content
        yield {
            'url': response.url,
            'title': response.css('title::text').get(),
            'content': response.css('body').get(),
            'links': response.css('a::attr(href)').getall(),
            'timestamp': response.headers.get('Date', '').decode('utf-8')
        }
        
        # Follow internal links
        links = response.css('a::attr(href)').getall()
        for link in links:
            absolute_url = urljoin(response.url, link)
            if self.is_internal_link(absolute_url):
                yield response.follow(
                    link,
                    callback=self.parse,
                    headers=response.request.headers
                )
    
    def is_internal_link(self, url):
        return any(domain in url for domain in self.allowed_domains)