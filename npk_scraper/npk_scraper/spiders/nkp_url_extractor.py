import scrapy
import re
from urllib.parse import urljoin


class NkpUrlExtractor<PERSON><PERSON><PERSON>(scrapy.Spider):
    name = 'nkp_urls'
    allowed_domains = ['nkp.gov.np']
    start_urls = ['https://nkp.gov.np/browse']

    def parse(self, response):
        """Extract all year URLs from browse page"""
        year_links = response.css('a[href*="browse_monthly"]::attr(href)').getall()
        
        for link in year_links:
            full_url = urljoin(response.url, link)
            print(f"Year URL: {full_url}")
            
            yield scrapy.Request(
                url=full_url,
                callback=self.parse_months,
                dont_filter=True
            )

    def parse_months(self, response):
        """Extract all month URLs from year pages"""
        month_links = response.css('a[href*="advance_search"]::attr(href)').getall()
        
        for link in month_links:
            full_url = urljoin(response.url, link)
            print(f"Month URL: {full_url}")
            
            # Just yield the URL without following it
            yield {
                'url': full_url,
                'type': 'month_url'
            }