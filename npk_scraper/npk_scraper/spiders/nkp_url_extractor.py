import scrapy
import re
from urllib.parse import urljoin, parse_qs, urlparse


class NkpUrlExtractor<PERSON><PERSON>er(scrapy.Spider):
    name = 'nkp_urls'
    allowed_domains = ['nkp.gov.np']
    start_urls = ['https://nkp.gov.np/browse']

    custom_settings = {
        'DOWNLOAD_DELAY': 1,
        'RANDOMIZE_DOWNLOAD_DELAY': True,
        'CONCURRENT_REQUESTS_PER_DOMAIN': 2,
        'USER_AGENT': 'Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0'
    }

    def parse(self, response):
        """Extract all year URLs from browse page"""
        year_links = response.css('a[href*="browse_monthly"]::attr(href)').getall()

        print(f"Found {len(year_links)} year links")

        for link in year_links:
            full_url = urljoin(response.url, link)

            # Extract year from URL for metadata
            parsed_url = urlparse(full_url)
            query_params = parse_qs(parsed_url.query)
            year = query_params.get('year', ['unknown'])[0]

            print(f"Processing year: {year} - URL: {full_url}")

            yield scrapy.Request(
                url=full_url,
                callback=self.parse_months,
                meta={'year': year},
                dont_filter=True
            )

    def parse_months(self, response):
        """Extract all month URLs from year pages"""
        year = response.meta.get('year', 'unknown')
        month_links = response.css('a[href*="advance_search"]::attr(href)').getall()

        print(f"Found {len(month_links)} month links for year {year}")

        for link in month_links:
            full_url = urljoin(response.url, link)

            # Extract month from URL for metadata
            parsed_url = urlparse(full_url)
            query_params = parse_qs(parsed_url.query)
            month = query_params.get('month', ['unknown'])[0]

            print(f"Processing year {year}, month {month}: {full_url}")

            yield scrapy.Request(
                url=full_url,
                callback=self.parse_cases,
                meta={'year': year, 'month': month},
                dont_filter=True
            )

    def parse_cases(self, response):
        """Extract individual case URLs from month pages"""
        year = response.meta.get('year', 'unknown')
        month = response.meta.get('month', 'unknown')

        # Extract case detail links
        case_links = response.css('a[href*="full_detail"]::attr(href)').getall()

        print(f"Found {len(case_links)} cases for year {year}, month {month}")

        for link in case_links:
            full_url = urljoin(response.url, link)

            # Extract case ID from URL
            case_id = link.split('/')[-1] if '/' in link else 'unknown'

            yield {
                'url': full_url,
                'type': 'case_detail',
                'year': year,
                'month': month,
                'case_id': case_id
            }

        # Handle pagination if exists
        next_page = response.css('a[href*="page="]::attr(href)').get()
        if next_page:
            next_url = urljoin(response.url, next_page)
            print(f"Following pagination: {next_url}")
            yield scrapy.Request(
                url=next_url,
                callback=self.parse_cases,
                meta={'year': year, 'month': month},
                dont_filter=True
            )